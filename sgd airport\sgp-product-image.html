<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新加坡专业接送机服务 - 产品宣传图</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@5.15.4/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <style>
        :root {
            --primary-blue: #0f172a;
            --secondary-blue: #1e40af;
            --accent-gold: #f59e0b;
            --dark-gold: #d97706;
            --light-gray: #f8fafc;
            --text-dark: #1e293b;
            --text-light: #64748b;
        }

        /* 垂直长图专用样式 */
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(180deg, #f1f5f9 0%, #e2e8f0 50%, #f8fafc 100%);
            color: var(--text-dark);
            line-height: 1.6;
            margin: 0;
            padding: 0;
            width: 1200px;
            margin: 0 auto;
            min-height: 4000px;
        }

        /* 主容器 */
        .main-container {
            width: 1200px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 50px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        /* 标题区域 */
        .hero-section {
            height: 600px;
            /* background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 50%, #3b82f6 100%); */ /* 背景由 img 元素提供 */
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            color: white;
            overflow: hidden;
        }

        .hero-section::before { /* 作为图片的半透明遮罩 */
            content: '';
            position: absolute;
            inset: 0;
            background-color: rgba(15, 23, 42, 0.5); /* 半透明深蓝色遮罩，可调整颜色和透明度 */
            z-index: 1; /* 遮罩在图片之上 */
        }

        .hero-content {
            position: relative;
            z-index: 2; /* 内容在遮罩之上 */
            max-width: 1000px;
            padding: 0 40px;
        }

        .hero-title {
            font-size: 4.5rem;
            font-weight: 900;
            margin-bottom: 30px;
            background: linear-gradient(45deg, #ffffff, var(--accent-gold), #ffffff);
            background-size: 200% 200%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradientShift 3s ease-in-out infinite;
            line-height: 1.1;
        }

        .hero-subtitle {
            font-size: 2rem;
            font-weight: 300;
            margin-bottom: 40px;
            opacity: 0.95;
        }

        .hero-features {
            display: flex;
            justify-content: center;
            gap: 40px;
            margin-top: 40px;
        }

        .hero-feature {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            padding: 20px 30px;
            border-radius: 50px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            font-size: 1.3rem;
            font-weight: 600;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        /* 分隔线样式 */
        .section-divider {
            height: 8px;
            background: linear-gradient(90deg, transparent, var(--accent-gold), var(--dark-gold), var(--accent-gold), transparent);
            margin: 0;
        }

        /* 内容区块样式 */
        .content-section {
            padding: 80px 60px;
            position: relative;
        }

        .section-title {
            font-size: 3.5rem;
            font-weight: 900;
            text-align: center;
            margin-bottom: 60px;
            color: var(--primary-blue);
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 6px;
            background: linear-gradient(90deg, var(--accent-gold), var(--dark-gold));
            border-radius: 3px;
        }

        .section-description {
            font-size: 1.5rem;
            text-align: center;
            color: var(--text-light);
            margin-bottom: 80px;
            max-width: 900px;
            margin-left: auto;
            margin-right: auto;
            line-height: 1.8;
        }

        /* 车型卡片样式 */
        .vehicle-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
            border-radius: 30px;
            margin-bottom: 40px;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            border: 2px solid rgba(245, 158, 11, 0.2);
            transition: all 0.3s ease;
            position: relative;
        }

        .vehicle-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, var(--accent-gold), var(--dark-gold), var(--secondary-blue));
        }

        .vehicle-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 30px 80px rgba(0, 0, 0, 0.25);
        }

        .vehicle-image {
            width: 100%;
            height: 300px;
            object-fit: cover;
            border-radius: 30px 30px 0 0;
        }

        .vehicle-content {
            padding: 50px;
        }

        .vehicle-title {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--primary-blue);
            margin-bottom: 30px;
            text-align: center;
        }

        .vehicle-specs {
            display: flex;
            justify-content: space-around;
            margin-bottom: 30px;
            gap: 20px;
        }

        .spec-item {
            background: linear-gradient(135deg, #e0f2fe, #f0f9ff);
            padding: 20px 30px;
            border-radius: 20px;
            text-align: center;
            flex: 1;
            border: 2px solid rgba(59, 130, 246, 0.2);
        }

        .spec-icon {
            font-size: 2rem;
            color: var(--secondary-blue);
            margin-bottom: 10px;
        }

        .spec-text {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--secondary-blue);
        }

        .vehicle-description {
            font-size: 1.4rem;
            color: var(--text-light);
            text-align: center;
            line-height: 1.8;
        }

        /* 导出按钮 */
        .export-button {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: linear-gradient(135deg, var(--accent-gold), var(--dark-gold));
            color: white;
            padding: 20px 30px;
            border-radius: 50px;
            border: none;
            font-size: 1.2rem;
            font-weight: 700;
            cursor: pointer;
            box-shadow: 0 10px 30px rgba(245, 158, 11, 0.4);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .export-button:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(245, 158, 11, 0.6);
        }

        /* 响应式优化 */
        @media (max-width: 1200px) {
            body, .main-container {
                width: 100%;
                max-width: 1200px;
            }
        }

        /* 航站楼信息样式 */
        .terminal-info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 60px;
        }

        .terminal-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
            border: 2px solid rgba(59, 130, 246, 0.2);
            transition: all 0.3s ease;
        }

        .terminal-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 60px rgba(0, 0, 0, 0.2);
        }

        .terminal-header {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
        }

        .terminal-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--secondary-blue), var(--primary-blue));
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            font-weight: 900;
            margin-right: 20px;
            box-shadow: 0 10px 25px rgba(30, 64, 175, 0.3);
        }

        .terminal-title {
            font-size: 2rem;
            font-weight: 800;
            color: var(--primary-blue);
            margin: 0;
        }

        .pickup-location {
            display: flex;
            align-items: center;
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--secondary-blue);
            margin-bottom: 20px;
            background: linear-gradient(135deg, #e0f2fe, #f0f9ff);
            padding: 15px 20px;
            border-radius: 15px;
            border: 2px solid rgba(59, 130, 246, 0.2);
        }

        .pickup-location i {
            font-size: 1.5rem;
            margin-right: 15px;
            color: var(--accent-gold);
        }

        .terminal-steps {
            margin-top: 20px;
        }

        .step-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            font-size: 1.1rem;
            color: var(--text-dark);
        }

        .step-number {
            width: 30px;
            height: 30px;
            background: linear-gradient(135deg, var(--accent-gold), var(--dark-gold));
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            margin-right: 15px;
            font-size: 0.9rem;
        }

        .terminal-description {
            font-size: 1.1rem;
            color: var(--text-light);
            line-height: 1.6;
            margin-top: 15px;
        }

        /* 服务特色样式 */
        .service-features {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-top: 60px;
        }

        .feature-item {
            text-align: center;
            padding: 50px 30px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
            border-radius: 25px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
            border: 2px solid rgba(245, 158, 11, 0.2);
            transition: all 0.3s ease;
        }

        .feature-item:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 60px rgba(0, 0, 0, 0.2);
        }

        .feature-icon {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, var(--accent-gold), var(--dark-gold));
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            margin: 0 auto 30px;
            box-shadow: 0 15px 35px rgba(245, 158, 11, 0.4);
        }

        .feature-title {
            font-size: 2rem;
            font-weight: 800;
            color: var(--primary-blue);
            margin-bottom: 20px;
        }

        .feature-description {
            font-size: 1.2rem;
            color: var(--text-light);
            line-height: 1.7;
        }

        /* 联系方式样式 */
        .contact-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 60px;
        }

        .contact-card {
            text-align: center;
            padding: 40px 30px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
            border-radius: 25px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
            border: 2px solid rgba(34, 197, 94, 0.2);
            transition: all 0.3s ease;
        }

        .contact-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 60px rgba(0, 0, 0, 0.2);
            border-color: rgba(34, 197, 94, 0.4);
        }

        .contact-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #22c55e, #16a34a);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.2rem;
            margin: 0 auto 25px;
            box-shadow: 0 15px 35px rgba(34, 197, 94, 0.4);
        }

        .contact-title {
            font-size: 1.8rem;
            font-weight: 800;
            color: var(--primary-blue);
            margin-bottom: 15px;
        }

        .contact-detail {
            font-size: 1.5rem;
            font-weight: 700;
            color: #22c55e;
            margin-bottom: 10px;
        }

        .contact-description {
            font-size: 1.1rem;
            color: var(--text-light);
        }

        /* 重要提醒样式 */
        .important-notice {
            background: linear-gradient(135deg, #fef3c7, #fde68a);
            border-radius: 25px;
            padding: 40px;
            border: 3px solid #f59e0b;
            margin-top: 60px;
        }

        .notice-header {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
        }

        .notice-header i {
            font-size: 2.5rem;
            color: #d97706;
            margin-right: 20px;
        }

        .notice-header h3 {
            font-size: 2.2rem;
            font-weight: 800;
            color: #92400e;
            margin: 0;
        }

        .notice-content ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .notice-content li {
            font-size: 1.3rem;
            color: #92400e;
            margin-bottom: 15px;
            padding-left: 30px;
            position: relative;
        }

        .notice-content li::before {
            content: '⚠️';
            position: absolute;
            left: 0;
            top: 0;
            font-size: 1.2rem;
        }

        /* 页脚样式 */
        .footer-section {
            background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
            color: white;
            padding: 60px 60px 40px;
            text-align: center;
        }

        .footer-content {
            max-width: 800px;
            margin: 0 auto;
        }

        .footer-logo h2 {
            font-size: 2.5rem;
            font-weight: 900;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #ffffff, var(--accent-gold));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .footer-logo p {
            font-size: 1.3rem;
            opacity: 0.9;
            margin-bottom: 30px;
        }

        .footer-info p {
            font-size: 1.1rem;
            opacity: 0.8;
            margin-bottom: 10px;
        }

        /* 图片点击替换功能样式 */
        .clickable-image {
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .clickable-image:hover {
            transform: scale(1.02);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .clickable-image::after {
            content: '📷 点击更换图片';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
            white-space: nowrap;
        }

        .clickable-image:hover::after {
            opacity: 1;
        }

        /* 旅游路线样式 */
        .tour-routes {
            display: grid;
            grid-template-columns: 1fr;
            gap: 40px;
            margin-top: 60px;
        }

        .route-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
            border-radius: 25px;
            overflow: hidden;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
            border: 2px solid rgba(16, 185, 129, 0.2);
            transition: all 0.3s ease;
        }

        .route-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 60px rgba(0, 0, 0, 0.2);
        }

        .route-header {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            padding: 30px 40px;
            text-align: center;
        }

        .route-title {
            font-size: 2.2rem;
            font-weight: 800;
            margin-bottom: 10px;
        }

        .route-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .route-content {
            padding: 40px;
        }

        .route-highlights {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .highlight-item {
            display: flex;
            align-items: center;
            font-size: 1.2rem;
            color: var(--text-dark);
        }

        .highlight-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 1.1rem;
        }

        .route-description {
            font-size: 1.3rem;
            color: var(--text-light);
            line-height: 1.7;
            text-align: center;
            background: linear-gradient(135deg, #f0fdf4, #dcfce7);
            padding: 25px;
            border-radius: 15px;
            border: 2px solid rgba(16, 185, 129, 0.2);
        }

        /* 订前须知样式 */
        .booking-notices {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-top: 60px;
        }

        .notice-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
            border: 2px solid rgba(239, 68, 68, 0.2);
            transition: all 0.3s ease;
        }

        .notice-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 60px rgba(0, 0, 0, 0.2);
        }

        .notice-card-header {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
        }

        .notice-card-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            margin-right: 20px;
            box-shadow: 0 10px 25px rgba(239, 68, 68, 0.3);
        }

        .notice-card-title {
            font-size: 1.8rem;
            font-weight: 800;
            color: var(--primary-blue);
            margin: 0;
        }

        .notice-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .notice-list li {
            font-size: 1.2rem;
            color: var(--text-dark);
            margin-bottom: 15px;
            padding-left: 30px;
            position: relative;
            line-height: 1.6;
        }

        .notice-list li::before {
            content: '⚠️';
            position: absolute;
            left: 0;
            top: 0;
            font-size: 1.1rem;
        }

        .notice-list li strong {
            color: #ef4444;
            font-weight: 700;
        }

        /* 打印和导出优化 */
        @media print {
            .export-button {
                display: none !important;
            }

            body {
                background: white;
                box-shadow: none;
            }

            .main-container {
                box-shadow: none;
            }

            .clickable-image::after {
                display: none !important;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 主标题区域 -->
        <section class="hero-section">
            <img src="https://c8.alamy.com/comp/2R6M2N2/aerial-view-of-marina-bay-sands-and-singapore-city-harbour-at-night-singapore-southeast-asia-asia-2R6M2N2.jpg" alt="新加坡滨海湾夜景" class="absolute inset-0 w-full h-full object-cover clickable-image" style="z-index: 0;">
            <div class="hero-content">
                <h1 class="hero-title">新加坡专业接送机服务</h1>
                <p class="hero-subtitle">新加坡旅行一站式服务 · 品质之选 · 安全保障</p>
                <div class="hero-features">
                    <div class="hero-feature">
                        <i class="fas fa-star mr-3"></i>专业品质
                    </div>
                    <div class="hero-feature">
                        <i class="fas fa-shield-alt mr-3"></i>安全保障
                    </div>
                    <div class="hero-feature">
                        <i class="fas fa-clock mr-3"></i>准时可靠
                    </div>
                </div>
            </div>
        </section>

        <!-- 分隔线 -->
        <div class="section-divider"></div>

        <!-- 车型选择区域 -->
        <section class="content-section">
            <h2 class="section-title">精选车型服务</h2>
            <p class="section-description">
                根据您的需求和人数，我们提供多种车型选择，从经济型轿车到小型巴士，满足不同的接送需求。每款车型都经过精心维护，确保您的出行安全舒适。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- 标准轿车 -->
                <div class="vehicle-card">
                    <img src="https://toyota-cms-media.s3.amazonaws.com/wp-content/uploads/2022/09/2023-toyota-rav4-hybrid-xse-interior-001.jpg" alt="标准轿车内饰" class="vehicle-image clickable-image">
                    <div class="vehicle-content">
                        <h3 class="vehicle-title">标准轿车</h3>
                        <div class="flex justify-between mb-4">
                            <div class="flex items-center bg-blue-50 px-3 py-1 rounded-lg">
                                <i class="fas fa-user text-blue-700 mr-2"></i>
                                <span class="font-semibold text-blue-700">4人</span>
                            </div>
                            <div class="flex items-center bg-amber-50 px-3 py-1 rounded-lg">
                                <i class="fas fa-suitcase text-amber-700 mr-2"></i>
                                <span class="font-semibold text-amber-700">3大2中行李</span>
                            </div>
                        </div>
                        <p class="vehicle-description">
                            经济实惠的选择，适合小型家庭或商务出行，提供舒适的乘坐体验。
                        </p>
                    </div>
                </div>

                <!-- 豪华轿车 -->
                <div class="vehicle-card">
                    <img src="https://www.mbusa.com/content/dam/mb-nafta/us/myco/my25/e-class/e-sedan/gallery/series/gallery-class/2025-E-SEDAN-GAL-026-Q-WP.jpg" alt="豪华轿车内饰" class="vehicle-image clickable-image">
                    <div class="vehicle-content">
                        <h3 class="vehicle-title">豪华轿车 (奔驰E级同级)</h3>
                        <div class="flex justify-between mb-4">
                            <div class="flex items-center bg-blue-50 px-3 py-1 rounded-lg">
                                <i class="fas fa-user text-blue-700 mr-2"></i>
                                <span class="font-semibold text-blue-700">4人</span>
                            </div>
                            <div class="flex items-center bg-amber-50 px-3 py-1 rounded-lg">
                                <i class="fas fa-suitcase text-amber-700 mr-2"></i>
                                <span class="font-semibold text-amber-700">3大3中行李</span>
                            </div>
                        </div>
                        <p class="vehicle-description">
                            高级商务之选，配备真皮座椅和豪华内饰，为您提供尊贵舒适的乘坐体验。
                        </p>
                    </div>
                </div>

                <!-- 商务MPV -->
                <div class="vehicle-card">
                    <img src="https://www.mundilimos.com/wp-content/uploads/2021/06/Sprinter-Van-Interior-05.jpg" alt="商务MPV内饰" class="vehicle-image clickable-image">
                    <div class="vehicle-content">
                        <h3 class="vehicle-title">商务MPV (丰田 Voxy)</h3>
                        <div class="flex justify-between mb-4">
                            <div class="flex items-center bg-blue-50 px-3 py-1 rounded-lg">
                                <i class="fas fa-user text-blue-700 mr-2"></i>
                                <span class="font-semibold text-blue-700">4人</span>
                            </div>
                            <div class="flex items-center bg-amber-50 px-3 py-1 rounded-lg">
                                <i class="fas fa-suitcase text-amber-700 mr-2"></i>
                                <span class="font-semibold text-amber-700">4大4中行李</span>
                            </div>
                        </div>
                        <p class="vehicle-description">
                            家人或朋友出行首选，宽敞舒适，拥有充足的乘坐空间和行李存放空间。
                        </p>
                    </div>
                </div>

                <!-- 丰田阿尔法 -->
                <div class="vehicle-card">
                    <img src="https://www.batfa.com/photo-newcar-alphardhybrid-interior.files/Alphard-interior-Flaxen-8.jpg" alt="丰田阿尔法内饰" class="vehicle-image clickable-image">
                    <div class="vehicle-content">
                        <h3 class="vehicle-title">丰田阿尔法/威尔法</h3>
                        <div class="flex justify-between mb-4">
                            <div class="flex items-center bg-blue-50 px-3 py-1 rounded-lg">
                                <i class="fas fa-user text-blue-700 mr-2"></i>
                                <span class="font-semibold text-blue-700">5人</span>
                            </div>
                            <div class="flex items-center bg-amber-50 px-3 py-1 rounded-lg">
                                <i class="fas fa-suitcase text-amber-700 mr-2"></i>
                                <span class="font-semibold text-amber-700">5大3中行李</span>
                            </div>
                        </div>
                        <p class="vehicle-description">
                            旗舰级豪华MPV，大空间配备顶级内饰，为团体旅行提供舒适享受。
                        </p>
                    </div>
                </div>

                <!-- 小巴 -->
                <div class="vehicle-card">
                    <img src="https://www.chicagovanrentals.com/userdata/vehicle/20240412_052643_704381.jpg" alt="小巴内部" class="vehicle-image clickable-image">
                    <div class="vehicle-content">
                        <h3 class="vehicle-title">小巴</h3>
                        <div class="flex justify-between mb-4">
                            <div class="flex items-center bg-blue-50 px-3 py-1 rounded-lg">
                                <i class="fas fa-user text-blue-700 mr-2"></i>
                                <span class="font-semibold text-blue-700">10人</span>
                            </div>
                            <div class="flex items-center bg-amber-50 px-3 py-1 rounded-lg">
                                <i class="fas fa-suitcase text-amber-700 mr-2"></i>
                                <span class="font-semibold text-amber-700">6大6中行李</span>
                            </div>
                        </div>
                        <p class="vehicle-description">
                            中型团体旅行首选，多人共乘经济实惠，舒适空调和专业司机服务。
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 行李规格参考 -->
        <section class="content-section bg-gray-50 rounded-lg p-8">
            <h2 class="section-title">行李规格详细参考</h2>
            <div class="mb-8">
                <h3 class="text-2xl font-semibold text-blue-800 mb-6">行李尺寸对比</h3>
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <div class="flex flex-col lg:flex-row justify-between items-center mb-8">
                        <img src="https://www.youcouldtravel.com/u/s/standard-suitcase-sizes-comparison-chart.jpg" alt="行李尺寸对比" class="w-full lg:w-1/2 rounded-lg mb-6 lg:mb-0 clickable-image">

                        <div class="w-full lg:w-1/2 lg:pl-8">
                            <table class="w-full border-collapse">
                                <thead>
                                    <tr class="bg-blue-900 text-white">
                                        <th class="border p-3 text-left">行李类型</th>
                                        <th class="border p-3 text-left">尺寸范围</th>
                                        <th class="border p-3 text-left">重量范围</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="border p-3 font-medium">大型行李<br>(28-32英寸)</td>
                                        <td class="border p-3">75-82厘米高</td>
                                        <td class="border p-3">20-32公斤</td>
                                    </tr>
                                    <tr class="bg-gray-50">
                                        <td class="border p-3 font-medium">中型行李<br>(24-27英寸)</td>
                                        <td class="border p-3">60-70厘米高</td>
                                        <td class="border p-3">15-25公斤</td>
                                    </tr>
                                    <tr>
                                        <td class="border p-3 font-medium">小型行李<br>(20-22英寸)</td>
                                        <td class="border p-3">50-55厘米高</td>
                                        <td class="border p-3">7-10公斤</td>
                                    </tr>
                                    <tr class="bg-gray-50">
                                        <td class="border p-3 font-medium">随身小包<br>(背包/手提包)</td>
                                        <td class="border p-3">≤40×30×20厘米</td>
                                        <td class="border p-3">通常≤7公斤</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
                        <div class="p-4 bg-blue-50 rounded-lg shadow">
                            <h4 class="font-bold text-blue-900 mb-2">行李容量参考</h4>
                            <ul class="list-disc pl-5 text-gray-700">
                                <li>大型行李：可装约3-4周的换洗衣物</li>
                                <li>中型行李：适合7-10天的旅行</li>
                                <li>小型行李：适合周末短途旅行</li>
                            </ul>
                        </div>

                        <div class="p-4 bg-blue-50 rounded-lg shadow">
                            <h4 class="font-bold text-blue-900 mb-2">航空行李重量限制</h4>
                            <ul class="list-disc pl-5 text-gray-700">
                                <li>经济舱：通常23公斤/件</li>
                                <li>商务/头等舱：通常32公斤/件</li>
                                <li>尺寸总和不超过158厘米(长+宽+高)</li>
                            </ul>
                        </div>

                        <div class="p-4 bg-blue-50 rounded-lg shadow">
                            <h4 class="font-bold text-blue-900 mb-2">特殊行李处理</h4>
                            <ul class="list-disc pl-5 text-gray-700">
                                <li>高尔夫球杆：可能需额外费用</li>
                                <li>儿童推车：只能接受能折叠型号</li>
                                <li>自行车：需提前预订特殊车型</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 分隔线 -->
        <div class="section-divider"></div>

        <!-- 热门旅游路线新版布局 -->
        <section class="content-section tour-section-gradient py-16 w-full max-w-[1920px] mx-auto px-6 lg:px-8">
            <h2 class="section-title tour-section-title">新加坡热门旅游路线</h2>
            <p class="section-description tour-section-desc">提供包车服务，带您探索新加坡的经典景点和隐藏宝藏。</p>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <!-- 市区经典路线卡片 -->
                <div class="vehicle-card tour-route-card"> <!-- Reuse vehicle-card for consistency, add tour specific classes -->
                    <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/b/b2/1_marina_bay_sands_skypark_night_view_CBD_skyline.jpg/2560px-1_marina_bay_sands_skypark_night_view_CBD_skyline.jpg" alt="市区经典路线" class="vehicle-image tour-card-img clickable-image">
                    <div class="vehicle-content tour-card-content">
                        <h3 class="vehicle-title tour-card-title">市区经典路线</h3>
                        <ul class="list-disc pl-5 text-gray-700 mb-4 tour-card-list">
                            <li>鱼尾狮公园 - 新加坡国家象征</li>
                            <li>滨海湾花园 - 未来主义植物园</li>
                            <li>牛车水 - 传统中华文化区</li>
                            <li>金沙空中花园 - 壮观城市全景</li>
                        </ul>
                        <p class="vehicle-description tour-card-desc">时长：约6小时，适合首次访问新加坡的旅客，体验狮城精华景点。</p>
                    </div>
                </div>
                <!-- 文化探索路线卡片 -->
                <div class="vehicle-card tour-route-card">
                    <img src="https://cpgconsultants.com.sg/wp-content/uploads/2022/03/Night-View-Changi-Airport-Terminal-3-Singapore-Large.jpg" alt="文化探索路线" class="vehicle-image tour-card-img clickable-image">
                    <div class="vehicle-content tour-card-content">
                        <h3 class="vehicle-title tour-card-title">文化探索路线</h3>
                        <ul class="list-disc pl-5 text-gray-700 mb-4 tour-card-list">
                            <li>新加坡国家博物馆 - 历史与文化展览</li>
                            <li>国家美术馆 - 东南亚艺术珍藏</li>
                            <li>小印度 - 丰富的印度文化体验</li>
                            <li>甘榜格南 - 马来文化区域</li>
                        </ul>
                        <p class="vehicle-description tour-card-desc">时长：约5小时，深入了解新加坡多元文化，适合文化爱好者。</p>
                    </div>
                </div>
                <!-- 家庭休闲路线卡片 -->
                <div class="vehicle-card tour-route-card">
                    <img src="https://i.ytimg.com/vi/WunLZMPQDnE/maxresdefault.jpg" alt="家庭休闲路线" class="vehicle-image tour-card-img clickable-image">
                    <div class="vehicle-content tour-card-content">
                        <h3 class="vehicle-title tour-card-title">家庭休闲路线</h3>
                        <ul class="list-disc pl-5 text-gray-700 mb-4 tour-card-list">
                            <li>环球影城 - 世界级主题公园</li>
                            <li>S.E.A海洋馆 - 超过100,000种海洋生物</li>
                            <li>新加坡动物园 - 开放式自然栖息地</li>
                            <li>夜间野生动物园 - 独特夜间动物体验</li>
                        </ul>
                        <p class="vehicle-description tour-card-desc">时长：约8小时，适合家庭旅行，孩子们会喜欢这些互动性强的景点。</p>
                    </div>
                </div>
                <!-- 购物天堂路线卡片 -->
                <div class="vehicle-card tour-route-card">
                    <img src="https://interiordesign.net/wp-content/uploads/2024/10/idx241001_boifillsarchitect08_2.jpg" alt="购物天堂路线" class="vehicle-image tour-card-img clickable-image">
                    <div class="vehicle-content tour-card-content">
                        <h3 class="vehicle-title tour-card-title">购物天堂路线</h3>
                        <ul class="list-disc pl-5 text-gray-700 mb-4 tour-card-list">
                            <li>乌节路 - 亚洲顶级购物街</li>
                            <li>ION Orchard - 豪华购物中心</li>
                            <li>滨海湾金沙购物中心 - 顶级品牌集中地</li>
                            <li>武吉士 - 年轻人潮流购物区</li>
                        </ul>
                        <p class="vehicle-description tour-card-desc">时长：约7小时，新加坡购物体验，从高端奢侈品到本地特色商品一应俱全。</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 分隔线 -->
        <div class="section-divider"></div>

        <!-- 航站楼指引区域 -->
        <section class="content-section">
            <h2 class="section-title">樟宜机场接机指引</h2>
            <p class="section-description">
                新加坡樟宜机场各航站楼指定上车点位置，我们的专业司机将在此处等候接机，确保您快速便捷地开始新加坡之旅。
            </p>

            <!-- 航站楼信息卡片 -->
            <div class="terminal-info-grid">
                <div class="terminal-card">
                    <div class="terminal-header">
                        <div class="terminal-icon">T1</div>
                        <h3 class="terminal-title">T1航站楼</h3>
                    </div>
                    <div class="terminal-content">
                        <div class="pickup-location">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>10-12号门 (地下一层B1)</span>
                        </div>
                        <div class="terminal-steps">
                            <div class="step-item">
                                <span class="step-number">1</span>
                                <span>寻找"Ride-Hailing & Arrival Pick-Up"指示牌</span>
                            </div>
                            <div class="step-item">
                                <span class="step-number">2</span>
                                <span>乘坐下行扶梯至地下一层</span>
                            </div>
                            <div class="step-item">
                                <span class="step-number">3</span>
                                <span>前往10-12号门等候司机</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="terminal-card">
                    <div class="terminal-header">
                        <div class="terminal-icon">T2</div>
                        <h3 class="terminal-title">T2航站楼</h3>
                    </div>
                    <div class="terminal-content">
                        <div class="pickup-location">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>3号门 (航站楼中央区域)</span>
                        </div>
                        <div class="terminal-description">
                            抵达迎宾大厅中央，靠近咨询柜台，然后前往3号门指定上车区域等候司机。
                        </div>
                    </div>
                </div>

                <div class="terminal-card">
                    <div class="terminal-header">
                        <div class="terminal-icon">T3</div>
                        <h3 class="terminal-title">T3航站楼</h3>
                    </div>
                    <div class="terminal-content">
                        <div class="pickup-location">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>1-2号门 (地下一层B1)</span>
                        </div>
                        <div class="terminal-description">
                            按照"Ride-Hailing & Arrival Pick-Up"指示牌，乘坐扶梯下至地下一层，在1-2号门等候。
                        </div>
                    </div>
                </div>

                <div class="terminal-card">
                    <div class="terminal-header">
                        <div class="terminal-icon">T4</div>
                        <h3 class="terminal-title">T4航站楼</h3>
                    </div>
                    <div class="terminal-content">
                        <div class="pickup-location">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>5-7号门</span>
                        </div>
                        <div class="terminal-description">
                            从到达大厅按照"Ride-Hailing Pick-up"标识，前往5-7号门等候司机接送。
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 分隔线 -->
        <div class="section-divider"></div>

        <!-- 服务特色区域 -->
        <section class="content-section">
            <h2 class="section-title">专业服务保障</h2>
            <p class="section-description">
                我们致力于为每一位客户提供安全、舒适、准时的接送服务，让您的新加坡之旅从踏出机场的那一刻就开始享受。
            </p>

            <div class="service-features">
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="feature-title">安全保障</h3>
                    <p class="feature-description">
                        所有车辆定期保养检查，司机经过专业培训，持有合法营运执照，为您的出行安全保驾护航。
                    </p>
                </div>

                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3 class="feature-title">准时可靠</h3>
                    <p class="feature-description">
                        实时航班监控，提前安排司机到位，确保您下飞机后能够第一时间见到我们的专业司机。
                    </p>
                </div>

                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <h3 class="feature-title">专业品质</h3>
                    <p class="feature-description">
                        多年新加坡接送服务经验，熟悉当地路况，提供最优路线规划，让您的旅程更加顺畅。
                    </p>
                </div>

                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <h3 class="feature-title">贴心服务</h3>
                    <p class="feature-description">
                        24小时客服支持，中文沟通无障碍，随时为您解答疑问，提供旅行建议和帮助。
                    </p>
                </div>
            </div>
        </section>

        <!-- 分隔线 -->
        <div class="section-divider"></div>

        <!-- 订前须知区域 -->
        <section class="content-section">
            <h2 class="section-title">订前须知</h2>
            <p class="section-description">
                为确保您的接送服务顺利进行，请仔细阅读以下重要信息和注意事项。
            </p>

            <div class="booking-notices">
                <!-- 预订政策 -->
                <div class="notice-card">
                    <div class="notice-card-header">
                        <div class="notice-card-icon">
                            <i class="fas fa-calendar-check"></i>
                        </div>
                        <h3 class="notice-card-title">预订政策</h3>
                    </div>
                    <ul class="notice-list">
                        <li><strong>提前预订：</strong>建议至少提前24小时预订，高峰期建议提前48小时</li>
                        <li><strong>确认时间：</strong>预订后2小时内确认，节假日可能延长至4小时</li>
                        <li><strong>航班变更：</strong>如航班时间变更，请至少提前2小时通知我们</li>
                        <li><strong>联系方式：</strong>请确保提供的手机号码在新加坡期间保持畅通</li>
                        <li><strong>特殊需求：</strong>儿童座椅、轮椅等特殊需求请在预订时说明</li>
                    </ul>
                </div>

                <!-- 取消退改 -->
                <div class="notice-card">
                    <div class="notice-card-header">
                        <div class="notice-card-icon">
                            <i class="fas fa-undo-alt"></i>
                        </div>
                        <h3 class="notice-card-title">取消退改</h3>
                    </div>
                    <ul class="notice-list">
                        <li><strong>免费取消：</strong>出发前24小时以上取消，全额退款</li>
                        <li><strong>部分退款：</strong>出发前12-24小时取消，退款50%</li>
                        <li><strong>不予退款：</strong>出发前12小时内取消或无故缺席，不予退款</li>
                        <li><strong>改期政策：</strong>出发前24小时以上可免费改期一次</li>
                        <li><strong>恶劣天气：</strong>因天气原因导致航班延误或取消，可全额退款</li>
                    </ul>
                </div>

                <!-- 服务条款 -->
                <div class="notice-card">
                    <div class="notice-card-header">
                        <div class="notice-card-icon">
                            <i class="fas fa-file-contract"></i>
                        </div>
                        <h3 class="notice-card-title">服务条款</h3>
                    </div>
                    <ul class="notice-list">
                        <li><strong>接机免费等待 60 分钟（以飞机落地时间起）</strong></li>
                        <li><strong>送机免费等待30分钟</strong></li>
                        <li><strong>超时费用：</strong>超过免费等待时间后，每15分钟收取额外费用</li>
                        <li><strong>行李限制：</strong>超出车型标准行李容量需提前说明</li>
                        <li><strong>路线选择：</strong>司机会选择最优路线，如有特殊要求请提前沟通</li>
                        <li><strong>费用包含：</strong>服务费用已包含过路费、停车费等</li>
                    </ul>
                </div>

                <!-- 安全须知 -->
                <div class="notice-card">
                    <div class="notice-card-header">
                        <div class="notice-card-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h3 class="notice-card-title">安全须知</h3>
                    </div>
                    <ul class="notice-list">
                        <li><strong>身份确认：</strong>司机会主动出示工作证件并确认您的身份</li>
                        <li><strong>车辆信息：</strong>上车前请核对车牌号码和司机信息</li>
                        <li><strong>安全带：</strong>请全程系好安全带，儿童请使用儿童座椅</li>
                        <li><strong>贵重物品：</strong>请随身携带贵重物品，不要遗留在车内</li>
                        <li><strong>紧急联系：</strong>如遇紧急情况，请立即联系我们的24小时客服</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- 分隔线 -->
        <div class="section-divider"></div>

            <!-- 重要提醒 -->
            <div class="important-notice">
                <div class="notice-header">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h3>重要提醒</h3>
                </div>
                <div class="notice-content">
                    <ul>
                        <li><strong>提前预订：</strong>建议至少提前24小时预订，确保车辆安排</li>
                        <li><strong>航班信息：</strong>请提供准确的航班号和到达时间</li>
                        <li><strong>联系方式：</strong>请保持手机畅通，司机会提前联系</li>
                        <li><strong>入境须知：</strong>中国护照免签30天，需填写电子入境卡</li>
                    </ul>
                </div>
            </div>
        </section>

    </div>

    <!-- 导出按钮 -->
    <button class="export-button" id="exportButton">
        <i class="fas fa-download mr-3"></i>
        导出产品图
    </button>

    <script>
        // 导出功能
        document.getElementById('exportButton').addEventListener('click', function() {
            // 隐藏导出按钮
            this.style.display = 'none';

            // 配置html2canvas选项
            const options = {
                useCORS: true,
                allowTaint: true,
                scale: 2, // 提高图片质量
                width: 1200,
                backgroundColor: '#ffffff',
                logging: false,
                onclone: function(clonedDoc) {
                    // 在克隆的文档中隐藏导出按钮
                    const clonedButton = clonedDoc.getElementById('exportButton');
                    if (clonedButton) {
                        clonedButton.style.display = 'none';
                    }
                }
            };

            // 使用html2canvas生成图片
            html2canvas(document.body, options).then(function(canvas) {
                // 创建下载链接
                const link = document.createElement('a');
                link.download = '新加坡接送机服务-产品宣传图.png';
                link.href = canvas.toDataURL('image/png', 1.0);

                // 触发下载
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // 显示成功消息
                alert('产品图导出成功！');

                // 重新显示导出按钮
                document.getElementById('exportButton').style.display = 'flex';
            }).catch(function(error) {
                console.error('导出失败:', error);
                alert('导出失败，请重试');

                // 重新显示导出按钮
                document.getElementById('exportButton').style.display = 'flex';
            });
        });

        // 页面加载完成后的优化
        window.addEventListener('load', function() {
            // 预加载所有图片
            const images = document.querySelectorAll('img');
            let loadedImages = 0;

            images.forEach(function(img) {
                if (img.complete) {
                    loadedImages++;
                } else {
                    img.addEventListener('load', function() {
                        loadedImages++;
                        if (loadedImages === images.length) {
                            console.log('所有图片加载完成，可以导出');
                        }
                    });
                }
            });

            // 添加平滑滚动
            document.documentElement.style.scrollBehavior = 'smooth';

            // 添加页面加载完成提示
            console.log('新加坡接送机服务产品图页面加载完成');
        });

        // 图片点击替换功能
        function setupImageReplacement() {
            const clickableImages = document.querySelectorAll('.clickable-image');

            clickableImages.forEach(function(img) {
                img.addEventListener('click', function() {
                    // 创建文件输入元素
                    const fileInput = document.createElement('input');
                    fileInput.type = 'file';
                    fileInput.accept = 'image/*';
                    fileInput.style.display = 'none';

                    // 处理文件选择
                    fileInput.addEventListener('change', function(event) {
                        const file = event.target.files[0];
                        if (file) {
                            // 检查文件类型
                            if (!file.type.startsWith('image/')) {
                                alert('请选择图片文件！');
                                return;
                            }

                            // 检查文件大小（限制为5MB）
                            if (file.size > 5 * 1024 * 1024) {
                                alert('图片文件大小不能超过5MB！');
                                return;
                            }

                            // 使用FileReader读取文件
                            const reader = new FileReader();
                            reader.onload = function(e) {
                                // 替换图片源
                                img.src = e.target.result;
                                console.log('图片已成功替换');
                            };

                            reader.onerror = function() {
                                alert('读取图片文件失败，请重试！');
                            };

                            reader.readAsDataURL(file);
                        }
                    });

                    // 触发文件选择
                    document.body.appendChild(fileInput);
                    fileInput.click();
                    document.body.removeChild(fileInput);
                });
            });
        }

        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化图片替换功能
            setupImageReplacement();

            // 为所有卡片添加点击效果
            const cards = document.querySelectorAll('.vehicle-card, .terminal-card, .feature-item, .contact-card, .route-card, .notice-card');
            cards.forEach(function(card) {
                card.addEventListener('click', function() {
                    this.style.transform = 'scale(0.98)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });

            // 添加图片加载完成提示
            console.log('页面加载完成，图片点击替换功能已启用');
        });
    </script>
</body>
</html>
